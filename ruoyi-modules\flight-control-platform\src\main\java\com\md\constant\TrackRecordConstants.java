package com.md.constant;

/**
 * 轨迹记录相关常量配置
 * 统一管理所有轨迹记录监听器的时间参数，确保集群部署下的一致性
 * 
 * <AUTHOR>
 * @since 2024-12-17
 */
public class TrackRecordConstants {

    /**
     * 最小记录间隔（毫秒）
     * 
     * 设计考虑：
     * - 定时任务触发间隔：1000ms
     * - 最小记录间隔：800ms  
     * - 时间缓冲区：200ms（20%缓冲，适应分布式环境的时钟偏差和网络延迟）
     * 
     * 调整原因：
     * 1. 原来950ms缓冲区只有50ms，在分布式环境下容易出现时间间隔控制不准确
     * 2. 200ms缓冲区能更好地应对：
     *    - 不同节点的系统时钟偏差（通常在几十毫秒内）
     *    - Redis网络延迟（通常在几毫秒到几十毫秒）
     *    - 分布式锁获取和释放的时间开销
     *    - JVM垃圾回收等系统级延迟
     */
    public static final long MIN_RECORD_INTERVAL = 800;

    /**
     * 定时任务执行间隔（毫秒）
     * 与@Scheduled(fixedRate = 1000)保持一致
     */
    public static final long SCHEDULED_INTERVAL = 1000;

    /**
     * 任务超时时间（毫秒）- 2小时
     * 防止任务异常终止后轨迹记录无限期继续
     */
    public static final long TASK_TIMEOUT = 2 * 60 * 60 * 1000;

    /**
     * 轨迹记录频率（每秒记录次数）
     * 基于最小记录间隔计算：1000ms / 800ms ≈ 1.25次/秒
     * 实际频率会根据定时任务触发和分布式锁竞争情况动态调整
     */
    public static final double RECORD_FREQUENCY = (double) SCHEDULED_INTERVAL / MIN_RECORD_INTERVAL;

    /**
     * 时间缓冲区大小（毫秒）
     * 定时任务间隔 - 最小记录间隔 = 缓冲区大小
     */
    public static final long TIME_BUFFER = SCHEDULED_INTERVAL - MIN_RECORD_INTERVAL;

    /**
     * 缓冲区比例（百分比）
     * 用于监控和告警，当实际间隔小于预期间隔的90%时触发告警
     */
    public static final double BUFFER_RATIO = (double) TIME_BUFFER / SCHEDULED_INTERVAL;

    /**
     * 异常间隔阈值（毫秒）
     * 当实际记录间隔小于此值时，认为是异常情况，需要记录告警日志
     */
    public static final long ABNORMAL_INTERVAL_THRESHOLD = (long) (MIN_RECORD_INTERVAL * 0.9);

    /**
     * Redis键过期时间配置
     */
    public static class RedisExpire {
        /** 活动任务过期时间（3小时） */
        public static final long ACTIVE_TASK_EXPIRE_HOURS = 3;
        
        /** 记录时间过期时间（10分钟） */
        public static final long RECORD_TIME_EXPIRE_MINUTES = 10;
        
        /** 任务开始时间过期时间（3小时） */
        public static final long START_TIME_EXPIRE_HOURS = 3;
    }

    /**
     * 分布式锁配置
     */
    public static class DistributedLock {
        /** 轨迹记录锁等待时间（秒） */
        public static final long TRACK_RECORD_WAIT_TIME = 1;
        
        /** 轨迹记录锁持有时间（秒） */
        public static final long TRACK_RECORD_LEASE_TIME = 10;
    }

    /**
     * 日志级别配置
     */
    public static class LogLevel {
        /** 是否启用调试日志 */
        public static final boolean DEBUG_ENABLED = false;
        
        /** 是否启用性能监控日志 */
        public static final boolean PERFORMANCE_LOG_ENABLED = true;
    }

    // 私有构造函数，防止实例化
    private TrackRecordConstants() {
        throw new UnsupportedOperationException("This is a constants class and cannot be instantiated");
    }
}
